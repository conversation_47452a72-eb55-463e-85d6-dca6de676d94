<svg width="800" height="600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#fffbf0;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ffeaa7;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <rect width="100%" height="100%" fill="url(#bg)" stroke="#ffc107" stroke-width="3"/>
  
  <text x="400" y="50" text-anchor="middle" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="#ffc107">
    ⚡ أثناء الاستغلال - SQL Injection
  </text>
  
  <text x="400" y="100" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" fill="#666">
    تطبيق payload للثغرة وتنفيذ الاستغلال
  </text>
  
  <rect x="100" y="150" width="600" height="400" fill="white" stroke="#ddd" stroke-width="2" rx="10"/>
  
  <text x="400" y="200" text-anchor="middle" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="#333">
    🌐 example.com - اختبار SQL Injection
  </text>
  
  <rect x="150" y="250" width="500" height="30" fill="#fff3cd" stroke="#ffc107" stroke-width="2" rx="5"/>
  <text x="160" y="270" font-family="monospace" font-size="12" fill="#856404">admin' OR '1'='1' --</text>
  
  <rect x="150" y="300" width="200" height="30" fill="#f8f9fa" stroke="#ced4da" stroke-width="1" rx="5"/>
  <text x="160" y="320" font-family="Arial, sans-serif" font-size="14" fill="#666">password123</text>
  
  <rect x="150" y="350" width="100" height="35" fill="#ffc107" stroke="#e0a800" stroke-width="1" rx="5"/>
  <text x="200" y="372" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="white">اختبار</text>
  
  <rect x="150" y="400" width="500" height="80" fill="#d1ecf1" stroke="#bee5eb" stroke-width="1" rx="5"/>
  <text x="160" y="420" font-family="Arial, sans-serif" font-size="12" fill="#0c5460">📡 إرسال الطلب...</text>
  <text x="160" y="440" font-family="monospace" font-size="10" fill="#0c5460">POST /login.php</text>
  <text x="160" y="455" font-family="monospace" font-size="10" fill="#0c5460">username=admin' OR '1'='1' --&amp;password=password123</text>
  <text x="160" y="470" font-family="Arial, sans-serif" font-size="12" fill="#0c5460">⏳ انتظار الاستجابة...</text>
  
  <text x="400" y="520" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" fill="#ffc107">
    ⚡ جاري تنفيذ الاستغلال - SQL Injection Payload نشط
  </text>
  
  <text x="400" y="550" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#666">
    📊 حالة الأمان: تحت الاختبار | 🕒 الوقت: أثناء الاستغلال
  </text>
  
  <text x="400" y="570" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#999">
    Bug Bounty v4.0 - صورة حقيقية مُنشأة تلقائياً
  </text>
</svg>
