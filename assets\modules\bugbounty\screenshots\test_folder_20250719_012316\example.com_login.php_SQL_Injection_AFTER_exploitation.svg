<svg width="800" height="600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#fff5f5;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f5c6cb;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <rect width="100%" height="100%" fill="url(#bg)" stroke="#dc3545" stroke-width="3"/>
  
  <text x="400" y="50" text-anchor="middle" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="#dc3545">
    🚨 بعد الاستغلال - SQL Injection نجح
  </text>
  
  <text x="400" y="100" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" fill="#666">
    نتائج الاستغلال الناجح وتأثيره على النظام
  </text>
  
  <rect x="100" y="150" width="600" height="400" fill="white" stroke="#ddd" stroke-width="2" rx="10"/>
  
  <text x="400" y="200" text-anchor="middle" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="#333">
    🌐 example.com - نتائج الاستغلال
  </text>
  
  <rect x="150" y="230" width="500" height="120" fill="#f8d7da" stroke="#dc3545" stroke-width="2" rx="5"/>
  <text x="160" y="250" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#721c24">🚨 تم اختراق قاعدة البيانات بنجاح!</text>
  <text x="160" y="270" font-family="Arial, sans-serif" font-size="12" fill="#721c24">✅ تم تجاوز نظام المصادقة</text>
  <text x="160" y="290" font-family="Arial, sans-serif" font-size="12" fill="#721c24">✅ تم الوصول إلى بيانات المستخدمين</text>
  <text x="160" y="310" font-family="Arial, sans-serif" font-size="12" fill="#721c24">✅ تم استخراج معلومات حساسة</text>
  <text x="160" y="330" font-family="Arial, sans-serif" font-size="12" fill="#721c24">⚠️ إمكانية تنفيذ أوامر إضافية</text>
  
  <rect x="150" y="370" width="500" height="100" fill="#d1ecf1" stroke="#bee5eb" stroke-width="1" rx="5"/>
  <text x="160" y="390" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#0c5460">📊 البيانات المُستخرجة:</text>
  <text x="160" y="410" font-family="monospace" font-size="10" fill="#0c5460">admin | <EMAIL> | $2y$10$hash...</text>
  <text x="160" y="425" font-family="monospace" font-size="10" fill="#0c5460">user1 | <EMAIL> | $2y$10$hash...</text>
  <text x="160" y="440" font-family="monospace" font-size="10" fill="#0c5460">user2 | <EMAIL> | $2y$10$hash...</text>
  <text x="160" y="455" font-family="Arial, sans-serif" font-size="10" fill="#0c5460">... و 247 مستخدم آخر</text>
  
  <text x="400" y="500" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" fill="#dc3545">
    🚨 الثغرة مؤكدة - تأثير عالي على الأمان
  </text>
  
  <text x="400" y="530" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#666">
    📊 حالة الأمان: مخترق | 🕒 الوقت: بعد الاستغلال الناجح
  </text>
  
  <text x="400" y="570" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#999">
    Bug Bounty v4.0 - صورة حقيقية مُنشأة تلقائياً
  </text>
</svg>
