// اختبار التحسينات الجديدة في نظام الصور Bug Bounty v4.0
const fs = require('fs');
const path = require('path');

// تحميل النظام
const BugBountyCore = require('./assets/modules/bugbounty/BugBountyCore.js');

async function testImprovedScreenshots() {
    console.log('🔥 اختبار التحسينات الجديدة في نظام الصور Bug Bounty v4.0...\n');
    
    try {
        // إنشاء مثيل من النظام
        const bugBounty = new BugBountyCore();
        
        // بيانات اختبار شاملة
        const testData = {
            target_url: 'https://example.com/test-page',
            vulnerabilities: [
                {
                    name: 'SQL Injection في صفحة تسجيل الدخول',
                    type: 'SQL Injection',
                    severity: 'Critical',
                    url: 'https://example.com/login.php',
                    parameter: 'username',
                    description: 'ثغرة SQL Injection في معامل username'
                },
                {
                    name: 'XSS Reflected في البحث',
                    type: 'XSS',
                    severity: 'High', 
                    url: 'https://example.com/search.php',
                    parameter: 'query',
                    description: 'ثغرة XSS منعكسة في صفحة البحث'
                },
                {
                    name: 'File Upload Vulnerability',
                    type: 'File Upload',
                    severity: 'High',
                    url: 'https://example.com/upload.php',
                    parameter: 'file',
                    description: 'ثغرة رفع ملفات خبيثة'
                }
            ]
        };
        
        console.log('📊 بيانات الاختبار:');
        console.log(`🎯 الرابط المستهدف: ${testData.target_url}`);
        console.log(`🔢 عدد الثغرات: ${testData.vulnerabilities.length}`);
        console.log('=' .repeat(80));
        
        // 1. اختبار إنشاء مجلدات الصور مع أسماء واضحة
        console.log('\n📁 المرحلة 1: اختبار إنشاء مجلدات الصور...');
        const reportId = `test_${Date.now()}`;
        const screenshotFolder = await bugBounty.generateScreenshotsForVulnerabilities(testData.vulnerabilities, reportId);
        
        if (screenshotFolder) {
            console.log(`✅ تم إنشاء مجلد الصور: ${screenshotFolder}`);
            
            // التحقق من وجود ملف معلومات المجلد
            const folderInfoPath = path.join(__dirname, 'assets', 'modules', 'bugbounty', 'screenshots', screenshotFolder, 'folder_info.txt');
            if (fs.existsSync(folderInfoPath)) {
                console.log('✅ تم إنشاء ملف معلومات المجلد');
                const infoContent = fs.readFileSync(folderInfoPath, 'utf8');
                console.log('📝 محتوى ملف المعلومات:');
                console.log(infoContent);
            } else {
                console.log('❌ لم يتم إنشاء ملف معلومات المجلد');
            }
        } else {
            console.log('❌ فشل في إنشاء مجلد الصور');
        }
        
        // 2. اختبار أسماء الصور الواضحة
        console.log('\n📸 المرحلة 2: اختبار أسماء الصور الواضحة...');
        for (let i = 0; i < testData.vulnerabilities.length; i++) {
            const vuln = testData.vulnerabilities[i];
            console.log(`\n🔍 الثغرة ${i + 1}: ${vuln.name}`);
            
            if (vuln.screenshots) {
                console.log('✅ تم إنشاء مسارات الصور:');
                console.log(`  📷 قبل: ${vuln.screenshots.before}`);
                console.log(`  ⚡ أثناء: ${vuln.screenshots.during}`);
                console.log(`  🚨 بعد: ${vuln.screenshots.after}`);
                console.log(`  📁 المجلد: ${vuln.screenshots.folder}`);
                console.log(`  📍 المسار: ${vuln.screenshots.folder_path}`);
                
                // التحقق من وجود الملفات الفعلية
                const screenshotDir = path.join(__dirname, 'assets', 'modules', 'bugbounty', 'screenshots', vuln.screenshots.folder);
                const beforeFile = path.join(screenshotDir, path.basename(vuln.screenshots.before));
                const duringFile = path.join(screenshotDir, path.basename(vuln.screenshots.during));
                const afterFile = path.join(screenshotDir, path.basename(vuln.screenshots.after));
                
                console.log('📂 التحقق من وجود الملفات:');
                console.log(`  📷 قبل: ${fs.existsSync(beforeFile) ? '✅ موجود' : '❌ غير موجود'}`);
                console.log(`  ⚡ أثناء: ${fs.existsSync(duringFile) ? '✅ موجود' : '❌ غير موجود'}`);
                console.log(`  🚨 بعد: ${fs.existsSync(afterFile) ? '✅ موجود' : '❌ غير موجود'}`);
            } else {
                console.log('❌ لم يتم إنشاء مسارات الصور');
            }
        }
        
        // 3. اختبار إنشاء التقرير مع الصور المحسنة
        console.log('\n📋 المرحلة 3: اختبار إنشاء التقرير مع الصور المحسنة...');
        
        const reportData = {
            scan_info: {
                scan_id: reportId,
                target_url: testData.target_url,
                total_vulnerabilities: testData.vulnerabilities.length,
                scan_date: new Date().toISOString()
            },
            vulnerabilities: testData.vulnerabilities,
            comprehensive_analysis: {
                total_score: 95.5,
                risk_level: 'عالي',
                recommendations: ['تطبيق إصلاحات فورية', 'تحديث أنظمة الحماية']
            }
        };
        
        // إنشاء التقرير الرئيسي
        const mainReport = await bugBounty.generateMainReport(reportData);
        const mainReportPath = `test_v4_improved_main_report.html`;
        fs.writeFileSync(mainReportPath, mainReport, 'utf8');
        console.log(`✅ تم إنشاء التقرير الرئيسي: ${mainReportPath}`);
        
        // إنشاء التقرير المنفصل
        const separateReport = await bugBounty.generateSeparateReport(reportData);
        const separateReportPath = `test_v4_improved_separate_report.html`;
        fs.writeFileSync(separateReportPath, separateReport, 'utf8');
        console.log(`✅ تم إنشاء التقرير المنفصل: ${separateReportPath}`);
        
        // 4. تحليل محتوى التقارير للتحقق من الصور
        console.log('\n🔍 المرحلة 4: تحليل محتوى التقارير...');
        
        // تحليل التقرير الرئيسي
        const mainReportAnalysis = {
            size: (mainReport.length / 1024).toFixed(2) + ' KB',
            hasScreenshots: mainReport.includes('screenshot') || mainReport.includes('صورة'),
            hasImageTags: mainReport.includes('<img'),
            hasScreenshotPaths: mainReport.includes('screenshots/'),
            hasFolderInfo: mainReport.includes('معلومات مجلد الصور'),
            screenshotCount: (mainReport.match(/<img/g) || []).length
        };
        
        console.log('📊 تحليل التقرير الرئيسي:');
        console.log(`  📏 الحجم: ${mainReportAnalysis.size}`);
        console.log(`  📸 يحتوي على صور: ${mainReportAnalysis.hasScreenshots ? '✅' : '❌'}`);
        console.log(`  🏷️ يحتوي على تاجات img: ${mainReportAnalysis.hasImageTags ? '✅' : '❌'}`);
        console.log(`  📁 يحتوي على مسارات صور: ${mainReportAnalysis.hasScreenshotPaths ? '✅' : '❌'}`);
        console.log(`  📋 يحتوي على معلومات المجلد: ${mainReportAnalysis.hasFolderInfo ? '✅' : '❌'}`);
        console.log(`  🔢 عدد الصور: ${mainReportAnalysis.screenshotCount}`);
        
        // تحليل التقرير المنفصل
        const separateReportAnalysis = {
            size: (separateReport.length / 1024).toFixed(2) + ' KB',
            hasScreenshots: separateReport.includes('screenshot') || separateReport.includes('صورة'),
            hasImageTags: separateReport.includes('<img'),
            hasScreenshotPaths: separateReport.includes('screenshots/'),
            hasFolderInfo: separateReport.includes('معلومات مجلد الصور'),
            screenshotCount: (separateReport.match(/<img/g) || []).length
        };
        
        console.log('\n📊 تحليل التقرير المنفصل:');
        console.log(`  📏 الحجم: ${separateReportAnalysis.size}`);
        console.log(`  📸 يحتوي على صور: ${separateReportAnalysis.hasScreenshots ? '✅' : '❌'}`);
        console.log(`  🏷️ يحتوي على تاجات img: ${separateReportAnalysis.hasImageTags ? '✅' : '❌'}`);
        console.log(`  📁 يحتوي على مسارات صور: ${separateReportAnalysis.hasScreenshotPaths ? '✅' : '❌'}`);
        console.log(`  📋 يحتوي على معلومات المجلد: ${separateReportAnalysis.hasFolderInfo ? '✅' : '❌'}`);
        console.log(`  🔢 عدد الصور: ${separateReportAnalysis.screenshotCount}`);
        
        // 5. النتائج النهائية
        console.log('\n' + '=' .repeat(80));
        console.log('📊 النتائج النهائية للتحسينات:');
        
        const improvements = {
            folderNaming: screenshotFolder && screenshotFolder.includes('scan_') ? '✅' : '❌',
            folderInfo: fs.existsSync(path.join(__dirname, 'assets', 'modules', 'bugbounty', 'screenshots', screenshotFolder || '', 'folder_info.txt')) ? '✅' : '❌',
            imageNaming: testData.vulnerabilities.every(v => v.screenshots?.before?.includes('_BEFORE_')) ? '✅' : '❌',
            pathStructure: testData.vulnerabilities.every(v => v.screenshots?.folder_path?.startsWith('./assets')) ? '✅' : '❌',
            reportIntegration: mainReportAnalysis.hasImageTags && separateReportAnalysis.hasImageTags ? '✅' : '❌'
        };
        
        console.log(`🏷️ تسمية المجلدات الواضحة: ${improvements.folderNaming}`);
        console.log(`📝 ملف معلومات المجلد: ${improvements.folderInfo}`);
        console.log(`📸 تسمية الصور الواضحة: ${improvements.imageNaming}`);
        console.log(`📁 هيكل المسارات الصحيح: ${improvements.pathStructure}`);
        console.log(`📋 تكامل التقارير: ${improvements.reportIntegration}`);
        
        const successCount = Object.values(improvements).filter(v => v === '✅').length;
        const totalCount = Object.keys(improvements).length;
        const successRate = ((successCount / totalCount) * 100).toFixed(1);
        
        console.log(`\n🎯 معدل نجاح التحسينات: ${successCount}/${totalCount} (${successRate}%)`);
        
        if (successRate >= 80) {
            console.log('🎉 تم تطبيق التحسينات بنجاح! نظام الصور محسن ويعمل بشكل ممتاز!');
        } else {
            console.log('⚠️ بعض التحسينات تحتاج إلى مراجعة إضافية');
        }
        
    } catch (error) {
        console.error('❌ خطأ في اختبار التحسينات:', error.message);
        console.error('📍 تفاصيل الخطأ:', error.stack);
    }
}

// تشغيل الاختبار
testImprovedScreenshots().then(() => {
    console.log('\n✅ انتهى اختبار التحسينات الجديدة في نظام الصور');
}).catch(error => {
    console.error('❌ خطأ في تشغيل اختبار التحسينات:', error);
});
