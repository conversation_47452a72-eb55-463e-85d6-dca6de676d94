// اختبار فعلي وحقيقي للنظام مع إنشاء الصور في اللحظة
const fs = require('fs');
const path = require('path');

// تحميل النظام
const BugBountyCore = require('./assets/modules/bugbounty/BugBountyCore.js');

async function testRealSystemWithImages() {
    console.log('🔥 اختبار فعلي وحقيقي للنظام مع إنشاء الصور في اللحظة...\n');
    
    try {
        // إنشاء مثيل من النظام
        const bugBounty = new BugBountyCore();
        
        // بيانات ثغرة حقيقية للاختبار
        const realVulnerability = {
            name: 'SQL Injection في صفحة تسجيل الدخول',
            type: 'SQL Injection',
            severity: 'Critical',
            url: 'https://testsite.com/login.php',
            target_url: 'https://testsite.com/login.php',
            parameter: 'username',
            payload: "admin' OR '1'='1' --",
            description: 'ثغرة SQL Injection خطيرة تسمح بتجاوز نظام المصادقة',
            impact: 'يمكن للمهاجم الوصول إلى قاعدة البيانات والحصول على بيانات المستخدمين',
            exploitation_confirmed: true,
            real_data: {
                request: "POST /login.php HTTP/1.1\nHost: testsite.com\nContent-Type: application/x-www-form-urlencoded\n\nusername=admin' OR '1'='1' --&password=test",
                response: "HTTP/1.1 200 OK\nLocation: /dashboard.php\nSet-Cookie: session=abc123"
            }
        };
        
        console.log('🎯 بيانات الثغرة الحقيقية:');
        console.log(`📍 الموقع: ${realVulnerability.url}`);
        console.log(`🔧 المعامل: ${realVulnerability.parameter}`);
        console.log(`💉 Payload: ${realVulnerability.payload}`);
        console.log(`⚠️ الخطورة: ${realVulnerability.severity}`);
        
        // إنشاء بيانات التقرير
        const reportData = {
            scan_info: {
                scan_id: `real_test_${Date.now()}`,
                target_url: realVulnerability.url,
                total_vulnerabilities: 1,
                scan_date: new Date().toISOString()
            },
            vulnerabilities: [realVulnerability],
            comprehensive_analysis: {
                total_score: 95.5,
                risk_level: 'عالي',
                critical: 1,
                high: 0,
                medium: 0,
                low: 0,
                recommendations: ['إصلاح فوري مطلوب', 'تطبيق Prepared Statements', 'تحديث أنظمة الحماية']
            }
        };
        
        console.log('\n🔥 بدء الاختبار الفعلي للنظام...');
        console.log('=====================================');
        
        // 1. إنشاء التقرير الرئيسي (سيقوم بإنشاء المجلد والصور تلقائياً)
        console.log('\n📊 1️⃣ إنشاء التقرير الرئيسي مع الصور...');
        const mainReport = await bugBounty.generateMainReport(reportData);
        
        // حفظ التقرير
        const mainReportPath = 'real_test_main_report.html';
        fs.writeFileSync(mainReportPath, mainReport, 'utf8');
        
        console.log(`✅ تم إنشاء التقرير الرئيسي: ${mainReportPath}`);
        console.log(`📊 حجم التقرير: ${(mainReport.length / 1024).toFixed(2)} KB`);
        
        // 2. فحص المجلدات المُنشأة
        console.log('\n📁 2️⃣ فحص المجلدات والصور المُنشأة...');
        const screenshotsDir = path.join(__dirname, 'assets', 'modules', 'bugbounty', 'screenshots');
        
        if (fs.existsSync(screenshotsDir)) {
            const folders = fs.readdirSync(screenshotsDir).filter(item => {
                const itemPath = path.join(screenshotsDir, item);
                return fs.statSync(itemPath).isDirectory();
            });
            
            console.log(`📂 عدد المجلدات الموجودة: ${folders.length}`);
            
            // العثور على أحدث مجلد (المُنشأ في هذا الاختبار)
            if (folders.length > 0) {
                const latestFolder = folders[folders.length - 1];
                const latestFolderPath = path.join(screenshotsDir, latestFolder);
                
                console.log(`\n📂 أحدث مجلد مُنشأ: ${latestFolder}`);
                
                // فحص محتويات المجلد
                const folderContents = fs.readdirSync(latestFolderPath);
                console.log(`📄 محتويات المجلد (${folderContents.length} عنصر):`);
                
                folderContents.forEach(item => {
                    const itemPath = path.join(latestFolderPath, item);
                    const stats = fs.statSync(itemPath);
                    const size = (stats.size / 1024).toFixed(2);
                    console.log(`  - ${item} (${size} KB)`);
                });
                
                // فحص الصور المُنشأة
                const imageFiles = folderContents.filter(file => 
                    file.endsWith('.svg') || file.endsWith('.png') || file.endsWith('.jpg')
                );
                
                console.log(`\n📸 الصور المُنشأة (${imageFiles.length} صورة):`);
                imageFiles.forEach(image => {
                    const imagePath = path.join(latestFolderPath, image);
                    const imageStats = fs.statSync(imagePath);
                    const imageSize = (imageStats.size / 1024).toFixed(2);
                    console.log(`  📷 ${image} (${imageSize} KB)`);
                });
                
                // 3. فحص التقرير للتأكد من تضمين الصور
                console.log('\n🔍 3️⃣ فحص التقرير للتأكد من تضمين الصور...');
                
                // البحث عن مراجع الصور في التقرير
                const imageReferences = [];
                const imgTagRegex = /<img[^>]+src=["']([^"']+)["'][^>]*>/gi;
                let match;
                
                while ((match = imgTagRegex.exec(mainReport)) !== null) {
                    imageReferences.push(match[1]);
                }
                
                console.log(`🖼️ مراجع الصور في التقرير: ${imageReferences.length}`);
                imageReferences.forEach((ref, index) => {
                    console.log(`  ${index + 1}. ${ref}`);
                });
                
                // فحص حالة الصور
                const imageStatusRegex = /حالة الصورة:\s*(.*?)(?=\n|$)/gi;
                const imageStatuses = [];
                
                while ((match = imageStatusRegex.exec(mainReport)) !== null) {
                    imageStatuses.push(match[1].trim());
                }
                
                console.log(`\n📊 حالات الصور في التقرير:`);
                imageStatuses.forEach((status, index) => {
                    const isFound = !status.includes('غير موجودة');
                    const statusIcon = isFound ? '✅' : '❌';
                    console.log(`  ${index + 1}. ${statusIcon} ${status}`);
                });
                
                // 4. إحصائيات النتائج
                console.log('\n📈 4️⃣ إحصائيات النتائج:');
                const foundImages = imageStatuses.filter(status => !status.includes('غير موجودة')).length;
                const totalImages = imageStatuses.length;
                const successRate = totalImages > 0 ? ((foundImages / totalImages) * 100).toFixed(1) : 0;
                
                console.log(`📊 الصور الموجودة: ${foundImages}/${totalImages}`);
                console.log(`📈 معدل النجاح: ${successRate}%`);
                console.log(`📂 المجلد المُنشأ: ${latestFolder}`);
                console.log(`📄 ملفات المجلد: ${folderContents.length}`);
                console.log(`🖼️ صور المجلد: ${imageFiles.length}`);
                
                // 5. تحليل المشكلة إذا وجدت
                if (foundImages < totalImages) {
                    console.log('\n⚠️ 5️⃣ تحليل المشكلة:');
                    console.log('❌ بعض الصور لم يتم العثور عليها في التقرير');
                    console.log('🔍 الأسباب المحتملة:');
                    console.log('  - عدم تطابق أسماء الملفات مع المسارات المتوقعة');
                    console.log('  - عدم تحديث دالة البحث عن الصور');
                    console.log('  - مشكلة في ربط المجلد المُنشأ بالتقرير');
                    
                    // عرض تفاصيل المقارنة
                    console.log('\n🔍 مقارنة الملفات المُنشأة مع المسارات المطلوبة:');
                    console.log('📁 الملفات المُنشأة:');
                    imageFiles.forEach(file => console.log(`  - ${file}`));
                    
                    console.log('\n📍 المسارات في التقرير:');
                    imageReferences.forEach(ref => console.log(`  - ${ref}`));
                } else {
                    console.log('\n🎉 5️⃣ النتيجة: نجح الاختبار بالكامل!');
                    console.log('✅ جميع الصور تم إنشاؤها وتضمينها بنجاح');
                }
                
            } else {
                console.log('❌ لم يتم العثور على أي مجلدات صور');
            }
        } else {
            console.log('❌ مجلد الصور غير موجود');
        }
        
        console.log('\n🏁 انتهى الاختبار الفعلي للنظام');
        console.log(`📄 التقرير المُنشأ: ${mainReportPath}`);
        
    } catch (error) {
        console.error('❌ خطأ في الاختبار الفعلي:', error.message);
        console.error('📍 تفاصيل الخطأ:', error.stack);
    }
}

// تشغيل الاختبار
testRealSystemWithImages().then(() => {
    console.log('\n✅ انتهى الاختبار الفعلي للنظام مع الصور');
}).catch(error => {
    console.error('❌ خطأ في تشغيل الاختبار الفعلي:', error);
});
