// اختبار النظام المُصلح مع الصور الفعلية
const fs = require('fs');
const path = require('path');

// تحميل النظام
const BugBountyCore = require('./assets/modules/bugbounty/BugBountyCore.js');

async function testFixedSystem() {
    console.log('🔧 اختبار النظام المُصلح مع الصور الفعلية...\n');
    
    try {
        // إنشاء مثيل من النظام
        const bugBounty = new BugBountyCore();
        
        // البحث عن مجلد الصور الموجود فعلياً
        const screenshotsDir = path.join(__dirname, 'assets', 'modules', 'bugbounty', 'screenshots');
        const folders = fs.readdirSync(screenshotsDir).filter(item => {
            const itemPath = path.join(screenshotsDir, item);
            return fs.statSync(itemPath).isDirectory();
        });
        
        console.log('📁 المجلدات الموجودة:');
        folders.forEach(folder => console.log(`  - ${folder}`));
        
        if (folders.length === 0) {
            console.log('❌ لا توجد مجلدات صور');
            return;
        }
        
        // استخدام أحدث مجلد
        const latestFolder = folders[folders.length - 1];
        const folderPath = path.join(screenshotsDir, latestFolder);
        
        console.log(`\n📂 استخدام المجلد: ${latestFolder}`);
        
        // قراءة الصور الموجودة
        const imageFiles = fs.readdirSync(folderPath).filter(file => file.endsWith('.svg'));
        console.log(`\n📸 عدد الصور الموجودة: ${imageFiles.length}`);
        imageFiles.forEach(file => console.log(`  - ${file}`));
        
        // إنشاء بيانات ثغرة تجريبية مع الصور الفعلية
        const testVulnerability = {
            name: 'SQL Injection Critical Test',
            type: 'SQL Injection',
            severity: 'Critical',
            url: 'https://example.com/login.php',
            target_url: 'https://example.com/login.php',
            description: 'ثغرة SQL Injection خطيرة في صفحة تسجيل الدخول',
            impact: 'يمكن للمهاجم الوصول إلى قاعدة البيانات بالكامل',
            screenshots: {
                folder: latestFolder,
                before: `./assets/modules/bugbounty/screenshots/${latestFolder}/${imageFiles.find(f => f.includes('BEFORE')) || imageFiles[0]}`,
                during: `./assets/modules/bugbounty/screenshots/${latestFolder}/${imageFiles.find(f => f.includes('DURING')) || imageFiles[1]}`,
                after: `./assets/modules/bugbounty/screenshots/${latestFolder}/${imageFiles.find(f => f.includes('AFTER')) || imageFiles[2]}`
            },
            visual_proof: {
                screenshot_folder: latestFolder,
                before_screenshot: `./assets/modules/bugbounty/screenshots/${latestFolder}/${imageFiles.find(f => f.includes('BEFORE')) || imageFiles[0]}`,
                during_screenshot: `./assets/modules/bugbounty/screenshots/${latestFolder}/${imageFiles.find(f => f.includes('DURING')) || imageFiles[1]}`,
                after_screenshot: `./assets/modules/bugbounty/screenshots/${latestFolder}/${imageFiles.find(f => f.includes('AFTER')) || imageFiles[2]}`
            }
        };
        
        console.log('\n🔧 اختبار دالة generateImageEvidence...');
        
        // اختبار دالة generateImageEvidence
        const imageEvidence = bugBounty.generateImageEvidence(testVulnerability);
        
        console.log(`✅ تم إنشاء HTML للصور: ${imageEvidence.length} حرف`);
        
        // فحص المحتوى للتأكد من عدم وجود البادئة الخاطئة
        const hasWrongPrefix = imageEvidence.includes('data:image/png;base64,./assets');
        const hasCorrectPaths = imageEvidence.includes('./assets/modules/bugbounty/screenshots/');
        
        console.log(`\n📊 نتائج الفحص:`);
        console.log(`❌ البادئة الخاطئة موجودة: ${hasWrongPrefix ? 'نعم' : 'لا'}`);
        console.log(`✅ المسارات الصحيحة موجودة: ${hasCorrectPaths ? 'نعم' : 'لا'}`);
        
        // إنشاء تقرير اختبار
        const reportData = {
            scan_info: {
                scan_id: `fixed_test_${Date.now()}`,
                target_url: testVulnerability.url,
                total_vulnerabilities: 1,
                scan_date: new Date().toISOString()
            },
            vulnerabilities: [testVulnerability],
            comprehensive_analysis: {
                total_score: 95.5,
                risk_level: 'عالي',
                critical: 1,
                high: 0,
                medium: 0,
                low: 0,
                recommendations: ['تطبيق إصلاحات فورية', 'تحديث أنظمة الحماية']
            }
        };
        
        console.log('\n🔧 إنشاء تقرير اختبار مُصلح...');
        
        // إنشاء التقرير الرئيسي
        const mainReport = await bugBounty.generateMainReport(reportData);
        const mainReportPath = 'test_fixed_main_report.html';
        fs.writeFileSync(mainReportPath, mainReport, 'utf8');
        
        console.log(`✅ تم إنشاء التقرير الرئيسي: ${mainReportPath}`);
        console.log(`📊 حجم التقرير: ${(mainReport.length / 1024).toFixed(2)} KB`);
        
        // فحص التقرير للتأكد من الإصلاحات
        const reportHasWrongPrefix = mainReport.includes('data:image/png;base64,./assets');
        const reportHasCorrectPaths = mainReport.includes('./assets/modules/bugbounty/screenshots/');
        const reportHasImages = mainReport.includes('<img src=');
        
        console.log(`\n📊 فحص التقرير النهائي:`);
        console.log(`❌ البادئة الخاطئة في التقرير: ${reportHasWrongPrefix ? 'نعم' : 'لا'}`);
        console.log(`✅ المسارات الصحيحة في التقرير: ${reportHasCorrectPaths ? 'نعم' : 'لا'}`);
        console.log(`🖼️ صور موجودة في التقرير: ${reportHasImages ? 'نعم' : 'لا'}`);
        
        // إنشاء التقرير المنفصل
        const separateReport = await bugBounty.generateSeparateReport(reportData);
        const separateReportPath = 'test_fixed_separate_report.html';
        fs.writeFileSync(separateReportPath, separateReport, 'utf8');
        
        console.log(`✅ تم إنشاء التقرير المنفصل: ${separateReportPath}`);
        console.log(`📊 حجم التقرير: ${(separateReport.length / 1024).toFixed(2)} KB`);
        
        // النتيجة النهائية
        const allFixed = !reportHasWrongPrefix && reportHasCorrectPaths && reportHasImages;
        
        console.log(`\n🎉 النتيجة النهائية: ${allFixed ? '✅ تم الإصلاح بنجاح' : '❌ يحتاج مزيد من الإصلاح'}`);
        
        if (allFixed) {
            console.log('\n🏆 الإصلاحات المُطبقة بنجاح:');
            console.log('✅ إزالة البادئة الخاطئة data:image/png;base64,./assets');
            console.log('✅ استخدام المسارات الديناميكية الصحيحة');
            console.log('✅ عرض الصور الفعلية في التقارير');
            console.log('✅ ربط الصور بالمجلدات الصحيحة');
        }
        
        console.log('\n📋 الملفات المُنشأة:');
        console.log(`  - ${mainReportPath} (التقرير الرئيسي المُصلح)`);
        console.log(`  - ${separateReportPath} (التقرير المنفصل المُصلح)`);
        
    } catch (error) {
        console.error('❌ خطأ في اختبار النظام المُصلح:', error.message);
        console.error('📍 تفاصيل الخطأ:', error.stack);
    }
}

// تشغيل الاختبار
testFixedSystem().then(() => {
    console.log('\n✅ انتهى اختبار النظام المُصلح');
}).catch(error => {
    console.error('❌ خطأ في تشغيل اختبار النظام المُصلح:', error);
});
